package com.moregames.playtime.user.gamerank.calculators

import com.moregames.base.messaging.dto.UserChallengeProgressDto
import com.moregames.base.messaging.dto.UserChallengeProgressDto.MilestoneProgressDto
import com.moregames.base.util.alert
import com.moregames.base.util.logger
import com.moregames.playtime.user.challenge.dto.ChallengeProgress
import com.moregames.playtime.user.challenge.progress.AbstractUniqueLevelProgressCalculator.Companion.MAX_LEVELS
import com.moregames.playtime.user.challenge.progress.achievement.AchievementDto
import com.moregames.playtime.user.gamerank.GameRank
import com.moregames.playtime.user.gamerank.GameRankConfig
import kotlin.math.min

class SolitaireGameRankProgressCalculator : GameRankProgressCalculator {
  override fun calculateProgress(progressDto: UserChallengeProgressDto, currentProgress: Int, currentAchievement: AchievementDto?, config: GameRankConfig): GameRankProgress {
    logger().debug("Calculating progress {}", progressDto)
    if (currentProgress == config.progressMax) {
      return GameRankProgress(GameRank.THREE, config.progressMax, currentAchievement)
    }
    val previousAchievement = currentAchievement ?: AchievementDto.empty()
    val achievementAddition = nextAchievement(progressDto)
    val nextAchievement = AchievementDto(previousAchievement.levels union achievementAddition.levels)
    if (nextAchievement.levels.size > MAX_LEVELS) {
      logger().alert("Numbers of level is too big $progressDto. Max level is $MAX_LEVELS")
      return GameRankProgress(GameRank.THREE, config.progressMax, previousAchievement)
    }
    val calculatedProgression = nextAchievement.levels.size
    val actualProgress = min(calculatedProgression, config.progressMax)
    val rank = when {
      actualProgress < config.oneStarProgressMax -> GameRank.ZERO
      actualProgress < config.twoStarProgressMax -> GameRank.ONE
      actualProgress < config.threeStarProgressMax -> GameRank.TWO
      else -> GameRank.THREE
    }
    return GameRankProgress(rank, actualProgress, nextAchievement)
  }

  private fun nextAchievement(progressDto: UserChallengeProgressDto): AchievementDto {
    val milestone = (progressDto as? MilestoneProgressDto)?.milestone ?: return AchievementDto.empty()
    if (milestone == 0) return AchievementDto.empty() //this is not end of first Solitaire game
    return AchievementDto(setOf(milestone.toString()))
  }
}